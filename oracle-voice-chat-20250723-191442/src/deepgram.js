/**
 * Optimized Deepgram STT Client
 * High-performance speech-to-text with connection pooling
 */

const axios = require('axios');
const FormData = require('form-data');

class DeepgramClient {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://api.deepgram.com/v1/listen';

        // Check if we have a real API key
        if (!this.apiKey || this.apiKey === 'your_deepgram_api_key_here') {
            console.log('🎤 Deepgram running in MOCK mode (no valid API key)');
            this.mockMode = true;
        } else {
            console.log('🎤 Deepgram running in PRODUCTION mode with API key');
            this.mockMode = false;
        }

        // Check if we have a real API key or should use mock mode
        this.mockMode = !this.apiKey || this.apiKey === 'your_deepgram_api_key_here';

        if (this.mockMode) {
            console.log('🎤 Deepgram running in MOCK mode (no API key)');
        } else {
            console.log('🎤 Deepgram running in PRODUCTION mode');
        }
        
        // Optimized HTTP client with connection pooling
        this.client = axios.create({
            timeout: 30000,
            maxRedirects: 0,
            httpAgent: new (require('http').Agent)({
                keepAlive: true,
                maxSockets: 10,
                maxFreeSockets: 5
            }),
            httpsAgent: new (require('https').Agent)({
                keepAlive: true,
                maxSockets: 10,
                maxFreeSockets: 5
            })
        });
        
        // Configurations with Slovak support and noise filtering
        this.configs = [
            {
                model: 'nova-2',
                language: 'sk',            // Slovak (supported by Nova-2)
                punctuate: true,
                smart_format: true,
                // Noise filtering parameters
                filler_words: false,       // Remove filler words (um, uh, etc.)
                numerals: true,           // Convert numbers to numerals
                // Enhanced speech detection
                detect_language: false,    // Don't auto-detect, use SK
                detect_topics: false,     // Don't detect topics
                summarize: false          // Don't summarize
            },
            {
                model: 'nova-2',
                language: 'cs',            // Czech fallback
                punctuate: true,
                smart_format: true,
                filler_words: false,
                numerals: true,
                detect_language: false,
                detect_topics: false,
                summarize: false
            },
            {
                model: 'nova-2',
                language: 'en',            // English fallback
                punctuate: true,
                smart_format: true,
                filler_words: false,
                numerals: true,
                detect_language: false,
                detect_topics: false,
                summarize: false
            }
        ];
        
        console.log('🎤 Deepgram client initialized with connection pooling');
    }
    
    async transcribe(audioBuffer) {
        const startTime = Date.now();

        // Mock mode - return simulated transcript
        if (this.mockMode) {
            const duration = Math.random() * 500 + 200; // 200-700ms
            await new Promise(resolve => setTimeout(resolve, duration));

            const mockTranscripts = [
                'Ahoj, ako sa máš?',
                'Toto je test slovenského rozpoznávania reči.',
                'Môžeš mi pomôcť s úlohou?',
                'Ďakujem za pomoc.',
                'Aké je dnes počasie?',
                'Dovidenia!'
            ];

            const mockResult = mockTranscripts[Math.floor(Math.random() * mockTranscripts.length)];
            const totalTime = Date.now() - startTime;

            console.log(`🎤 Deepgram MOCK STT: ${totalTime}ms - "${mockResult}"`);
            return mockResult;
        }

        // Production mode - use real Deepgram API
        // Pre-process audio for better speech detection
        const processedAudio = await this.preprocessAudio(audioBuffer);

        // Try configurations in order until we get a good result
        for (let i = 0; i < this.configs.length; i++) {
            try {
                const result = await this.transcribeWithConfig(processedAudio, this.configs[i]);

                // Enhanced result validation
                if (this.isValidSpeech(result)) {
                    const duration = Date.now() - startTime;
                    console.log(`🎤 Deepgram STT success (config ${i + 1}): ${duration}ms - "${result}"`);
                    return result.trim();
                }

                console.log(`🎤 Config ${i + 1} returned invalid/empty transcript: "${result}"`);

            } catch (error) {
                console.error(`🎤 Config ${i + 1} failed:`, error.message);

                // If it's the last config, throw the error
                if (i === this.configs.length - 1) {
                    throw error;
                }
            }
        }

        throw new Error('All Deepgram configurations failed or returned invalid speech');
    }
    
    async transcribeWithConfig(audioBuffer, config) {
        // Build URL with query parameters
        const params = new URLSearchParams();
        Object.entries(config).forEach(([key, value]) => {
            params.append(key, value.toString());
        });

        const url = `${this.baseUrl}?${params.toString()}`;

        console.log(`🔗 Deepgram request: ${config.language} model with ${Object.keys(config).length} params`);

        try {
            const response = await this.client.post(url, audioBuffer, {
                headers: {
                    'Authorization': `Token ${this.apiKey}`,
                    'Content-Type': 'audio/wav',
                    'User-Agent': 'FastVoiceServer/1.0'
                },
                timeout: 15000, // 15 second timeout
                maxContentLength: 50 * 1024 * 1024, // 50MB max
            });

            console.log(`📡 Deepgram response: ${response.status} ${response.statusText}`);

            // Enhanced response parsing
            const data = response.data;
            if (!data || !data.results) {
                console.error('❌ Invalid Deepgram response structure:', data);
                throw new Error('Invalid response structure from Deepgram');
            }

            const channels = data.results.channels;
            if (!channels || channels.length === 0) {
                console.error('❌ No channels in Deepgram response');
                throw new Error('No audio channels found in response');
            }

            const alternatives = channels[0].alternatives;
            if (!alternatives || alternatives.length === 0) {
                console.error('❌ No alternatives in Deepgram response');
                return ''; // Empty but valid response
            }

            const transcript = alternatives[0].transcript;
            const confidence = alternatives[0].confidence || 0;

            console.log(`📝 Deepgram transcript: "${transcript}" (confidence: ${confidence})`);

            // Log additional alternatives if available
            if (alternatives.length > 1) {
                console.log(`📝 Alternative transcripts: ${alternatives.slice(1).map(alt => `"${alt.transcript}" (${alt.confidence})`).join(', ')}`);
            }

            return transcript || '';

        } catch (error) {
            if (error.response) {
                const status = error.response.status;
                const data = error.response.data;

                console.error(`❌ Deepgram API error ${status}:`, data);

                if (status === 400) {
                    throw new Error(`Deepgram bad request: ${data?.message || 'Invalid audio format'}`);
                } else if (status === 401) {
                    throw new Error('Deepgram authentication failed - check API key');
                } else if (status === 402) {
                    throw new Error('Deepgram payment required - check account balance');
                } else if (status === 429) {
                    throw new Error('Deepgram rate limit exceeded - try again later');
                } else if (status >= 500) {
                    throw new Error(`Deepgram server error ${status} - try again later`);
                } else {
                    throw new Error(`Deepgram API error ${status}: ${data?.message || 'Unknown error'}`);
                }
            } else if (error.request) {
                console.error('❌ Deepgram network error:', error.message);
                throw new Error('Deepgram API network error - check internet connection');
            } else {
                console.error('❌ Deepgram client error:', error.message);
                throw new Error(`Deepgram client error: ${error.message}`);
            }
        }
    }
    
    async preprocessAudio(audioBuffer) {
        // Basic audio preprocessing for better speech detection
        console.log(`🔧 Preprocessing audio: ${audioBuffer.length} bytes`);

        // Check minimum audio length (at least 0.5 seconds at 16kHz)
        const minBytes = 16000 * 2 * 0.5; // 16kHz * 2 bytes * 0.5 seconds
        if (audioBuffer.length < minBytes) {
            console.log(`⚠️ Audio too short: ${audioBuffer.length} bytes (min: ${minBytes})`);
            throw new Error('Audio too short for reliable speech detection');
        }

        // Check maximum audio length (max 30 seconds)
        const maxBytes = 16000 * 2 * 30; // 30 seconds max
        if (audioBuffer.length > maxBytes) {
            console.log(`⚠️ Audio too long: ${audioBuffer.length} bytes (max: ${maxBytes})`);
            // Truncate to last 30 seconds
            return audioBuffer.slice(-maxBytes);
        }

        return audioBuffer;
    }

    isValidSpeech(transcript) {
        if (!transcript || typeof transcript !== 'string') {
            return false;
        }

        const cleaned = transcript.trim().toLowerCase();

        // Check minimum length
        if (cleaned.length < 2) {
            console.log(`🔍 Transcript too short: "${cleaned}"`);
            return false;
        }

        // Filter out common noise patterns
        const noisePatterns = [
            /^[.,!?;:\s]*$/,           // Only punctuation and spaces
            /^(uh|um|eh|ah|hm)+$/i,    // Filler words only
            /^[0-9\s]*$/,              // Only numbers
            /^[^a-záčďéíĺľňóôŕšťúýž]*$/i, // No actual letters (Slovak + English)
            /^\s*$/, // Only whitespace
        ];

        for (const pattern of noisePatterns) {
            if (pattern.test(cleaned)) {
                console.log(`🔍 Filtered out noise: "${cleaned}" (pattern: ${pattern})`);
                return false;
            }
        }

        // Check for meaningful content (at least one word with 2+ characters)
        const words = cleaned.split(/\s+/).filter(word => word.length >= 2);
        if (words.length === 0) {
            console.log(`🔍 No meaningful words found: "${cleaned}"`);
            return false;
        }

        console.log(`✅ Valid speech detected: "${cleaned}" (${words.length} words)`);
        return true;
    }

    async transcribeStreaming(audioStream) {
        // TODO: Implement streaming transcription with VAD
        throw new Error('Streaming transcription not yet implemented');
    }
    
    // Health check
    async healthCheck() {
        try {
            // Create a minimal audio buffer for testing
            const testBuffer = Buffer.alloc(1024);
            await this.transcribeWithConfig(testBuffer, this.configs[0]);
            return true;
        } catch (error) {
            console.error('Deepgram health check failed:', error.message);
            return false;
        }
    }
    
    // Get client statistics
    getStats() {
        return {
            baseUrl: this.baseUrl,
            configCount: this.configs.length,
            hasApiKey: !!this.apiKey
        };
    }
}

module.exports = DeepgramClient;
