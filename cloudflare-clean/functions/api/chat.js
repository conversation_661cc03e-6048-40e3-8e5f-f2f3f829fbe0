/**
 * Cloudflare Pages Function - Oracle Backend Chat Proxy
 */

const ORACLE_BACKEND = '129.159.9.170';

exports.onRequestPost = async function(context) {
    try {
        console.log('🔧 Proxying chat request to Oracle backend...');
        
        const requestBody = await context.request.text();
        console.log(`📡 Chat request: ${requestBody}`);
        
        // Forward request to Oracle backend
        const oracleResponse = await fetch(`https://${ORACLE_BACKEND}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Cloudflare-Pages-Function/1.0'
            },
            body: requestBody
        });
        
        console.log(`📡 Oracle chat response: ${oracleResponse.status}`);
        
        if (oracleResponse.ok) {
            const responseData = await oracleResponse.json();
            console.log(`✅ Chat successful: "${responseData.response}"`);
            
            return new Response(JSON.stringify(responseData), {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        } else {
            const errorText = await oracleResponse.text();
            console.error(`❌ Oracle chat error: ${errorText}`);
            
            return new Response(JSON.stringify({
                error: 'Chat failed',
                details: errorText
            }), {
                status: oracleResponse.status,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }
        
    } catch (error) {
        console.error('❌ Chat proxy error:', error);
        
        return new Response(JSON.stringify({
            error: 'Chat proxy error',
            message: error.message
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

exports.onRequestOptions = async function(context) {
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
