/**
 * Cloudflare Pages Function - Oracle Backend Proxy
 * Bypasses CORS by proxying requests to Oracle backend
 */

const ORACLE_BACKEND = '129.159.9.170';
const ORACLE_PORT = '443';

exports.onRequestPost = async function(context) {
    try {
        console.log('🔧 Proxying transcribe request to Oracle backend...');
        
        // Get the request body (FormData with audio)
        const requestBody = await context.request.arrayBuffer();
        const contentType = context.request.headers.get('content-type') || '';
        
        console.log(`📡 Proxying ${requestBody.byteLength} bytes to Oracle backend`);
        console.log(`📡 Content-Type: ${contentType}`);
        
        // Forward request to Oracle backend
        const oracleResponse = await fetch(`https://${ORACLE_BACKEND}/api/transcribe`, {
            method: 'POST',
            headers: {
                'Content-Type': contentType,
                'User-Agent': 'Cloudflare-Pages-Function/1.0'
            },
            body: requestBody,
            // Ignore SSL certificate issues
            cf: {
                // Cloudflare-specific options
                cacheTtl: 0,
                cacheEverything: false
            }
        });
        
        console.log(`📡 Oracle response: ${oracleResponse.status} ${oracleResponse.statusText}`);
        
        if (oracleResponse.ok) {
            const responseData = await oracleResponse.json();
            console.log(`✅ Transcription successful: "${responseData.transcript}"`);
            
            return new Response(JSON.stringify(responseData), {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type'
                }
            });
        } else {
            const errorText = await oracleResponse.text();
            console.error(`❌ Oracle backend error: ${oracleResponse.status} - ${errorText}`);
            
            return new Response(JSON.stringify({
                error: 'Transcription failed',
                details: errorText,
                status: oracleResponse.status
            }), {
                status: oracleResponse.status,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }
        
    } catch (error) {
        console.error('❌ Pages Function error:', error);
        
        return new Response(JSON.stringify({
            error: 'Proxy error',
            message: error.message,
            stack: error.stack
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

exports.onRequestOptions = async function(context) {
    // Handle CORS preflight
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400'
        }
    });
}
